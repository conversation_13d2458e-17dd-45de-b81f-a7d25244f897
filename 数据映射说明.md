# 数据映射说明文档

> **目的**: 说明如何将 `new product.md` 中的旅游路线信息转换为注册表单标准格式  
> **转换原则**: 数据标准化、占位符明确、便于后续编辑  

---

## 一、数据源分析

### 原始数据结构 (`new product.md`)
```
原始格式：
- 产品代码 (如: KUL-DT01)
- 产品名称 (如: 武吉免登城市打卡)
- 核心站点 (如: 国油双子塔-茨厂街-独立广场-中央市场)
- 司机任务 (如: 市区环线＋每站下客点等候)
- 备注 (如: 纯市内 ≤ 40 km)
```

### 目标数据结构 (注册表单格式)
```
目标格式：
- Tour (产品名称)
- PIC Details (负责人信息)
- Tour Details (旅游详情)
- Reservation Details (预订详情)
- Tour Highlights (旅游亮点)
- Pick up Service (接送服务)
- Additional Information (附加信息)
- Tour Itinerary (行程安排)
- Terms and Conditions (条款和条件)
- Pricing and Package Details (价格和套餐详情)
- Validity (有效性)
- Transport Type (交通类型)
- Refund Policy (退款政策)
```

---

## 二、字段映射关系

### 直接映射字段

| 原始字段 | 目标字段 | 映射方式 | 示例 |
|----------|----------|----------|------|
| 产品代码 | Tour | 直接复制 | KUL-DT01 |
| 产品名称 | Tour | 直接复制 | 武吉免登城市打卡 |
| 核心站点 | Tour Highlights / Address | 转换为亮点描述 | 国油双子塔-茨厂街-独立广场-中央市场 |
| 司机任务 | Additional Information | 转换为注意事项 | 市区环线＋每站下客点等候 |
| 备注 | Tour Highlights | 转换为特色说明 | 纯市内 ≤ 40 km |

### 推导映射字段

| 目标字段 | 推导依据 | 默认值/规则 | 说明 |
|----------|----------|-------------|------|
| Duration | 产品类型 | 一日游8-12小时，多日游按天数 | 根据行程复杂度估算 |
| Type of Tour | 核心站点特征 | 观光游览/文化历史/自然生态等 | 根据景点类型分类 |
| Location | 出发地代码 | KUL=吉隆坡, PEN=槟城, JHB=柔佛 | 从产品代码提取 |
| Pick up Time | 标准化要求 | 08:00 | 统一标准出发时间 |

### 占位符字段

| 目标字段 | 占位符类型 | 说明 |
|----------|------------|------|
| PIC Details | [待补充] | 需要指定具体负责人 |
| Pricing | [待补充] | 需要成本分析和市场调研 |
| Pictures | [待补充图片链接] | 需要收集产品图片 |
| Specific Terms | [根据实际情况调整] | 需要根据产品特点定制 |

---

## 三、转换规则说明

### 产品代码规则
```
格式: [出发地代码]-[产品类型][序号]
- KUL = 吉隆坡 (Kuala Lumpur)
- PEN = 槟城 (Penang)  
- JHB = 柔佛 (Johor Bahru)
- DT = 一日游 (Day Tour)
- MD = 多日游 (Multi-Day)
```

### 持续时间推算规则
```
一日游时间估算:
- 市区游览: 8小时
- 近郊景点: 10小时  
- 远程目的地: 12小时
- 夜间行程: 8小时 (下午-晚上)

多日游时间:
- 2天1夜: 48小时总时长
- 3天2夜: 72小时总时长
- 4天3夜: 96小时总时长
```

### 旅游类型分类规则
```
根据核心站点特征分类:
- 历史建筑/博物馆 → 文化历史游
- 自然景观/公园 → 自然生态游
- 主题公园/娱乐设施 → 主题娱乐游
- 市场/餐厅/烹饪 → 美食体验游
- 海滩/海岛 → 海滩休闲游
- 夜间活动 → 夜间特色游
- 购物中心/奥特莱斯 → 购物游
```

### 亮点提取规则
```
从原始信息提取亮点:
1. 核心站点 → 主要景点亮点
2. 司机任务 → 服务特色亮点  
3. 备注信息 → 独特卖点亮点
4. 标准化要素 → 服务标准亮点
5. 补充信息 → 体验价值亮点
```

---

## 四、标准化处理

### 统一格式标准
```
时间格式: 24小时制 (如: 08:00, 18:00)
价格格式: RM XXX (马来西亚林吉特)
年龄格式: XX-XX岁 (如: 12-99岁)
人数格式: X人 (如: 2人, 10人)
```

### 默认值设定
```
标准默认值:
- 出发时间: 08:00
- 回程时间: ≤18:00  
- 凭证类型: E-Voucher (QR code)
- 预订要求: 是
- 提前预订: 最少1天，建议3天
- 成人年龄: 12-99岁
- 儿童年龄: 3-11岁
- 免费年龄: 3岁以下
- 车辆类型: 空调巴士/12座面包车
```

### 占位符标准
```
占位符类型及用途:
- [待补充]: 完全缺失的信息
- [TBD]: 待确定的信息  
- [手动输入]: 需要手动填写的特定信息
- [根据实际情况调整]: 需要根据具体情况调整的信息
```

---

## 五、质量控制

### 数据完整性检查
```
必填字段检查:
□ 产品代码唯一性
□ 产品名称准确性
□ 基本信息完整性
□ 时间安排合理性
□ 价格结构一致性
```

### 内容质量检查
```
内容质量标准:
□ 描述语言专业且吸引人
□ 安全注意事项完整
□ 服务标准清晰明确
□ 退款政策合理公平
□ 联系信息准确有效
```

### 格式规范检查
```
格式规范要求:
□ 标题层级正确
□ 列表格式统一
□ 占位符标记清晰
□ 时间格式标准
□ 价格格式一致
```

---

## 六、转换后处理

### 立即需要完成
1. **信息补充**: 填写所有占位符字段
2. **价格设定**: 根据成本和市场确定价格
3. **内容优化**: 提升描述的吸引力和准确性
4. **图片收集**: 为每个产品准备宣传图片

### 后续优化工作
1. **供应商对接**: 确认门票、餐食、住宿合作
2. **系统集成**: 导入OTA平台和预订系统
3. **质量监控**: 建立客户反馈机制
4. **持续改进**: 根据运营数据优化产品

---

## 七、转换成果

### 转换统计
- **原始产品数量**: 30个 (25个一日游 + 5个多日游)
- **转换完成度**: 100% (基础框架)
- **字段完整度**: 约30% (需要补充占位符信息)
- **可用性**: 可作为产品开发基础模板

### 输出文件
1. **标准化产品资料列表.md**: 完整的产品信息模板
2. **产品汇总表.md**: 产品概览和统计信息
3. **数据映射说明.md**: 本转换说明文档

### 使用建议
1. 以标准化产品资料列表为主要工作文档
2. 使用产品汇总表进行快速查看和统计
3. 参考数据映射说明理解转换逻辑
4. 按照质量控制清单逐步完善信息

---

**文档版本**: v1.0  
**创建时间**: 2024年12月  
**适用范围**: Traveloka产品注册表单数据转换  
**维护责任**: 产品开发团队
